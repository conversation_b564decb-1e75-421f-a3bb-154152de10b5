// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import Divider from "@mui/material/Divider";
import { Link } from "react-router-dom";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";

import {useTranslation} from "react-i18next";
import {tracker} from "@/context";
import { useLocation } from "react-router-dom";

function HelpSupport() {

  const {t} = useTranslation();
  const location = useLocation();
  const { pathname } = location;

  const actionButtonStyles = {
    "cursor" : "pointer",
    "& .material-icons-round": {
      transform: `translateX(0)`,
      transition: "all 200ms cubic-bezier(0.34,1.61,0.7,1.3)",
    },

    "&:hover .material-icons-round, &:focus .material-icons-round": {
      transform: `translateX(4px)`,
    },
  };

  return (
    <Card id="support" sx={{ backgroundColor: 'white', overflow: 'hidden' }}>
      <MDBox p={3} lineHeight={1}>
        <MDBox mb={1}>
          <MDTypography variant="h5">
            {t("support-settings-title")}
          </MDTypography>
        </MDBox>
        <MDTypography variant="button" color="text" fontWeight="regular">
          {t("support-description")}
        </MDTypography>
      </MDBox>
      <MDBox pb={3} px={3} sx={{ overflow: "auto" }}>
      <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          width={{ xs: "max-content", sm: "100%" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDBox textAlign="center" px={{ xs: 0, md: 1.5 }} opacity={0.6}>
              <Icon fontSize="default" color="dark">chat</Icon>
            </MDBox>
            <MDBox height="100%" ml={2} lineHeight={1} mr={2}>
              <MDTypography display="block" variant="button" fontWeight="regular">
                {t("live-chat")}
              </MDTypography>
              <MDTypography variant="caption">
                {t("live-chat-description")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox display="flex" alignItems="center">
            <MDTypography
              variant="button"
              color="info"
              onClick={tracker.intercom.show}
              fontWeight="regular"
              sx={actionButtonStyles}
            >
              {t("chat-now")}
              &nbsp;
              <Icon sx={{ fontWeight: "bold", verticalAlign: "middle" }}>arrow_forward</Icon>
            </MDTypography>
          </MDBox>
        </MDBox>
        <Divider />
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          width={{ xs: "max-content", sm: "100%" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDBox textAlign="center" px={{ xs: 0, md: 1.5 }} opacity={0.6}>
              <Icon fontSize="default">help_center_outlined</Icon>
            </MDBox>
            <MDBox height="100%" ml={2} lineHeight={1} mr={2}>
              <MDTypography display="block" variant="button" fontWeight="regular">
                {t("help-center")}
              </MDTypography>
              <MDTypography variant="caption">
                {t("help-center-description")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox display="flex" alignItems="center">
            <MDTypography
              component="a"
              href="https://help.datadrew.io"
              target="_blank"
              variant="button"
              color="info"
              onClick={() => {tracker.mixpanel.track('Clicked HelpCenter', {page : pathname, source: "settings"})}}
              fontWeight="regular"
              sx={actionButtonStyles}
            >
              {t("see-more")}
              &nbsp;
              <Icon sx={{ fontWeight: "bold", verticalAlign: "middle" }}>arrow_forward</Icon>
            </MDTypography>
          </MDBox>
        </MDBox>
        <Divider />
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          width={{ xs: "max-content", sm: "100%" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDBox textAlign="center" px={{ xs: 0, md: 1.5 }} opacity={0.6}>
              <Icon fontSize="default">event_available</Icon>
            </MDBox>
            <MDBox height="100%" ml={2} lineHeight={1} mr={2}>
              <MDTypography display="block" variant="button" fontWeight="regular">
                {t("book-call")}
              </MDTypography>
              <MDTypography variant="caption">
                {t("book-a-call-description")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox display="flex" alignItems="center">
            <MDTypography
              component={Link}
              onClick={() => {tracker.mixpanel.track('Clicked BookCall', {page : pathname, source: "settings"})}}
              to="/book-call"
              variant="button"
              color="info"
              fontWeight="regular"
              sx={actionButtonStyles}
            >
              {t("see-more")}
              &nbsp;
              <Icon sx={{ fontWeight: "bold", verticalAlign: "middle" }}>arrow_forward</Icon>
            </MDTypography>
          </MDBox>
        </MDBox>
        <Divider />
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          width={{ xs: "max-content", sm: "100%" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDBox textAlign="center" px={{ xs: 0, md: 1.5 }} opacity={0.6}>
              <Icon fontSize="default">important_devices</Icon>
            </MDBox>
            <MDBox height="100%" ml={2} lineHeight={1} mr={2}>
              <MDTypography display="block" variant="button" fontWeight="regular">
                {t("feature-requests")}
              </MDTypography>
              <MDTypography variant="caption">
                {t("feature-requests-description")}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox display="flex" alignItems="center">
            <MDTypography
              onClick={() => {tracker.mixpanel.track('Feature Request Clicked', {page : pathname, source: "settings_support"})}}
              component={Link}
              to="/feature-requests"
              variant="button"
              color="info"
              fontWeight="regular"
              sx={actionButtonStyles}
            >
              {t("see-more")}
              &nbsp;
              <Icon sx={{ fontWeight: "bold", verticalAlign: "middle" }}>arrow_forward</Icon>
            </MDTypography>
          </MDBox>
        </MDBox>
        <Divider />
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          width={{ xs: "max-content", sm: "100%" }}
        >
          <MDBox display="flex" alignItems="center">
            <MDBox textAlign="center" px={{ xs: 0, md: 1.5 }} opacity={0.6}>
              <Icon fontSize="default">support</Icon>
            </MDBox>
            <MDBox height="100%" ml={2} lineHeight={1} mr={2}>
              <MDTypography display="block" variant="button" fontWeight="regular">
                {t("support")}
              </MDTypography>
              <MDTypography variant="caption">
                <EMAIL>
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox display="flex" alignItems="center">
            <MDTypography
              component="a"
              onClick={() => {tracker.mixpanel.track('Clicked Support', {page : pathname, source: "settings_support_email"})}}
              href="mailto:<EMAIL>"
              variant="button"
              color="info"
              fontWeight="regular"
              sx={actionButtonStyles}
            >
              {t("write-to-us")}
              &nbsp;
              <Icon sx={{ fontWeight: "bold", verticalAlign: "middle" }}>arrow_forward</Icon>
            </MDTypography>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
}

export default HelpSupport;
